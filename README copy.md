# groupie-tracker

Groupie Tracker is a web application that consumes and displays data about musical artists and their concert information through an interactive interface. The application fetches data from a provided API and presents it in a user-friendly way.

## Features
- Display detailed information about various artists/bands
- Interactive data visualization
- Responsive design for various screen sizes
- Client-server architecture with event handling

## Installation
1. Ensure you have Go installed on your system (version 1.16 or higher recommended)

2. Clone this repository:
   git clone https://github.com/hajaraljafen/groupie-tracker.git
   cd groupie-tracker

3. Run the application:
   go run .

4. Open your web browser and navigate to `http://localhost:4040`

## Usage
1. The homepage displays a list of artists/bands
2. Click on any artist to view their detailed information


## Project Structure

## API Endpoints
The application uses the following API endpoints:
- `/artists` - Artist information
- `/locations` - Concert locations
- `/dates` - Concert dates
- `/relation` - Relationships between data



## Dependencies
- Standard Go libraries only
- No external dependencies required