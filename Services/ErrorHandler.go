package Services

import (
	"html/template"
	"net/http"
	"path/filepath"
)

type ErrorResponse struct {
	StatusCode int
	StatusText string
}

func RenderError(w http.ResponseWriter, r *http.Request, statusCode int) {
	w.<PERSON>er().Set("Content-Type", "text/html; charset=utf-8")
	w.<PERSON><PERSON><PERSON><PERSON>(statusCode)

	var errPage string
	switch statusCode {
	case http.StatusInternalServerError:
		errPage = "error500.html"
	case http.StatusBadRequest:
		errPage = "error400.html"
	case http.StatusNotFound:
		errPage = "error404.html"
	case http.StatusMethodNotAllowed:
		errPage = "error405.html"
	default:
		errPage = "error500.html"
	}

	t, err := template.ParseFiles(filepath.Join("templates/errors", errPage))
	if err != nil {
		http.Error(w, "Error template not found", http.StatusInternalServerError)
		return
	}

	errData := ErrorResponse{
		StatusCode: statusCode,
		StatusText: http.StatusText(statusCode),
	}

	t.Execute(w, errData)
}
