package Services

import (
	models "GROUPIE_TRACKER/Models"
	"encoding/json"
	"fmt"
	"net/http"
)

func fetchArtists() ([]models.Artists, error) {
	resp, err := http.Get("https://groupietrackers.herokuapp.com/api/artists")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch artists: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %s", resp.Status)
	}

	var artists []models.Artists
	if err := json.NewDecoder(resp.Body).Decode(&artists); err != nil {
		return nil, fmt.Errorf("failed to decode artists data: %v", err)
	}
	return artists, nil
}

func fetchLocations() ([]models.LocationItem, error) {
	resp, err := http.Get("https://groupietrackers.herokuapp.com/api/locations")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch locations: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %s", resp.Status)
	}

	var locationsResp models.Location
	if err := json.NewDecoder(resp.Body).Decode(&locationsResp); err != nil {
		return nil, fmt.Errorf("failed to decode locations data: %v", err)
	}

	return locationsResp.Index, nil
}

func fetchArtistById(id int) ([]models.Artists, error) {
	resp, err := http.Get(fmt.Sprintf("https://groupietrackers.herokuapp.com/api/artists/%d", id))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch artist: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %s", resp.Status)
	}

	var artist models.Artists
	if err := json.NewDecoder(resp.Body).Decode(&artist); err != nil {
		return nil, fmt.Errorf("failed to decode artist data: %v", err)
	}

	if artist.ID == 0 || artist.Name == "" {
		return nil, nil
	}

	return []models.Artists{artist}, nil
}

func fetchDates() ([]models.DateItems, error) {
	resp, err := http.Get("https://groupietrackers.herokuapp.com/api/dates")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch dates: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %s", resp.Status)
	}

	var datesResp models.Dates
	if err := json.NewDecoder(resp.Body).Decode(&datesResp); err != nil {
		return nil, fmt.Errorf("failed to decode dates data: %v", err)
	}

	return datesResp.Index, nil
}

func fetchRelationData(id int) (map[string][]string, error) {
	resp, err := http.Get(fmt.Sprintf("https://groupietrackers.herokuapp.com/api/relation/%d", id))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch relation data: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return make(map[string][]string), nil
	}

	var relationData struct {
		ID             int                 `json:"id"`
		DatesLocations map[string][]string `json:"datesLocations"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&relationData); err != nil {
		return make(map[string][]string), nil
	}

	// If DatesLocations is nil, return an empty map
	if relationData.DatesLocations == nil {
		return make(map[string][]string), nil
	}

	return relationData.DatesLocations, nil
}

// func fetchLocationsById(id int) ([]models.LocationItem, error) {
// 	resp, err := http.Get(fmt.Sprintf("https://groupietrackers.herokuapp.com/api/locations/%d", id))
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to fetch locations: %v", err)
// 	}
// 	defer resp.Body.Close()

// 	if resp.StatusCode != http.StatusOK {
// 		return nil, fmt.Errorf("API request failed with status: %s", resp.Status)
// 	}

// 	var locationById models.Location
// 	if err := json.NewDecoder(resp.Body).Decode(&locationById); err != nil {
// 		return nil, fmt.Errorf("failed to decode location data: %v", err)
// 	}

// 	return locationById.Index, nil
// }
