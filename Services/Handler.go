package Services

import (
	models "GROUPIE_TRACKER/Models"
	//"log"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"text/template"
	"time"
	"unicode"
)

// titleCase converts a string to title case without using deprecated strings.Title
func titleCase(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(strings.ToLower(s))
	runes[0] = unicode.ToUpper(runes[0])
	for i := 1; i < len(runes); i++ {
		if unicode.IsSpace(runes[i-1]) {
			runes[i] = unicode.ToUpper(runes[i])
		}
	}
	return string(runes)
}

func getTemplate(w http.ResponseWriter, r *http.Request, tmpl string, data interface{}) {
	funcMap := template.FuncMap{
		"formatDate": func(dateStr string) string {
			if dateStr == "" {
				return ""
			}
			layouts := []string{
				"2006-01-02 15:04:05",
				"2006-01-02",
				"02-01-2006",
				time.RFC3339,
			}
			for _, layout := range layouts {
				t, err := time.Parse(layout, dateStr)
				if err == nil {
					if layout == "2006-01-02 15:04:05" || layout == time.RFC3339 {
						return t.Format("January 2, 2006 at 15:04")
					}
					return t.Format("January 2, 2006")
				}
			}
			return dateStr
		},
		"formatLocation": func(loc string) string {
			if loc == "" {
				return ""
			}
			parts := strings.Split(loc, "_")
			for i, part := range parts {
				part = strings.ReplaceAll(part, "-", " ")
				switch strings.ToLower(part) {
				case "usa":
					part = "USA"
				case "uk":
					part = "UK"
				case "uae":
					part = "UAE"
				default:
					part = titleCase(part)
				}
				parts[i] = part
			}
			return strings.Join(parts, ", ")
		},
	}

	tmplPath := filepath.Join("templates", tmpl)
	templ, err := template.New(filepath.Base(tmpl)).Funcs(funcMap).ParseFiles(tmplPath)
	if err != nil {
		RenderError(w, r, http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	if err := templ.Execute(w, data); err != nil {
		RenderError(w, r, http.StatusInternalServerError)
	}
}

func LoadHome(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	artists, err := fetchArtists()
	if err != nil {
		RenderError(w, r, http.StatusInternalServerError)
		return
	}
	getTemplate(w, r, "indexTest.html", artists)
}

type ArtistPageData struct {
	models.Artists
	ActiveTab string
}

func LoadArtist(w http.ResponseWriter, r *http.Request) {
	activeTab := r.URL.Query().Get("tab")
	if activeTab == "" {
		activeTab = "overview" 
	}

	if activeTab != "overview" && activeTab != "events" {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	idStr := strings.TrimPrefix(r.URL.Path, "/artist/")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		RenderError(w, r, http.StatusBadRequest)
		return
	}

	artists, err := fetchArtistById(id)
	if err != nil {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	if len(artists) == 0 {
		RenderError(w, r, http.StatusNotFound)
		return
	}

	artist := artists[0]
	datesLocations, err := fetchRelationData(id)
	if err != nil {
		datesLocations = make(map[string][]string)
	}

	if datesLocations == nil {
		datesLocations = make(map[string][]string)
	}

	artist.DatesLocations = datesLocations

	pageData := ArtistPageData{
		Artists:   artist,
		ActiveTab: activeTab,
	}

	getTemplate(w, r, "artist.html", pageData)
}
