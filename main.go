package main

import (
	"GROUPIE_TRACKER/Services"
	"fmt"
	"net/http"
)

func main() {

	// Serve static files from the static directory
	// fs := http.FileServer(http.Dir("static"))
	// http.Handle("/static/", http.StripPrefix("/static/", fs))

	http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("static"))))


	// Handle routes
	http.HandleFunc("/", Services.LoadHome)
	http.HandleFunc("/artist/", Services.LoadArtist) // Note the trailing slash to catch all /artist/*

	fmt.Println("Server started on http://localhost:4040")
	http.ListenAndServe(":4040", nil)
}
