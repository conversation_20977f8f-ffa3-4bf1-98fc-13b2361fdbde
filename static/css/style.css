/* Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary: #1db954;
  --primary-dark: #1aa34a;
  --black: #0f0f0f;
  --dark-gray: #181818;
  --medium-gray: #282828;
  --light-gray: #b3b3b3;
  --white: #ffffff;
  --gradient: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #0f0f0f;
  color: white;
  line-height: 1.6;
  padding: 1.25rem 5vw;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
  color: inherit;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

h5 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.header-mid {
  font-size: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 3.125rem;
  height: 0.25rem;
  background: var(--primary);
  border-radius: 2px;
}

p {
  color: var(--light-gray);
  margin-bottom: 1.5rem;
}

/* Tabs */
.artist-tabs {
  max-width: 1200px;
  margin: 0 auto 2em;
  padding: 0 5vw;
  border-bottom: 1px solid #333;
  position: relative;
}

.artist-tabs::after {
  content: '';
  display: table;
  clear: both;
}

/* Tab navigation */
.tab {
  display: inline-block;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  padding: 0.75em 1.5em;
  text-decoration: none;
  position: relative;
  opacity: 0.7;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.tab:hover {
  opacity: 1;
}

/* Tab content container */
.tab-content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  min-height: 40vh;
}

/* Tab content */
.tab-content {
  display: none;
  padding: 20px 0;
  animation: fadeIn 0.3s ease;
}

/* Default state - show overview content when no hash */
#overview:not(:target) {
  display: block;
}

/* Active tab styles */
#overview:target ~ .artist-tabs #overview-tab,
#events:target ~ .artist-tabs #events-tab,
#overview:not(:target) ~ .artist-tabs #overview-tab {
  opacity: 1;
  color: var(--primary);
}

#overview:target ~ .artist-tabs #overview-tab::after,
#events:target ~ .artist-tabs #events-tab::after,
#overview:not(:target) ~ .artist-tabs #overview-tab::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary);
  border-radius: 3px 3px 0 0;
}

/* Show events when its tab is targeted */
#events:target ~ .tab-content-container #overview {
  display: none;
}

#events:target ~ .tab-content-container #events {
  display: block;
}

/* Show overview when its tab is targeted */
#overview:target ~ .tab-content-container #overview {
  display: block;
}

#overview:target ~ .tab-content-container #events {
  display: none;
}

/* Hide events by default */
#events {
  display: none;
}

/* Animation for tab content */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Tab content styles */
.tab-content {
  display: none;
}

/* Show the active tab content */
#overview:target,
#events:target,
#overview:target ~ #overview,
#events:target ~ #events {
  display: block;
  animation: fadeIn 0.3s ease;
}

/* Hide the default content when a tab is targeted */
#overview:target ~ #events,
#events:target ~ #overview {
  display: none;
}

/* Show the default content when no tab is targeted */
#overview:not(:target) ~ #overview:not(:target) {
  display: block;
}

/* Hide the default content when any tab is targeted */
#overview:target ~ #overview:not(:target),
#events:target ~ #events:not(:target) {
  display: none;
}

/* Artist Bio Styles */
.artist-bio {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.artist-bio-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.artist-bio-content {
  flex: 1;
}

.artist-bio p {
  color: #b3b3b3;
  line-height: 1.6;
  margin-bottom: 24px;
}

.artist-stats {
  display: flex;
  gap: 32px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #282828;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.75rem;
  color: #b3b3b3;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75em 1.75em;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: var(--white);
  border: 1px solid var(--white);
  margin: 0 10px;
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.btn-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Navigation */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25em 5vw;
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--white);
  background: linear-gradient(45deg, var(--primary), #4cc9f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1px;
}

.nav-links {
  display: flex;
  gap: 30px;
}

.nav-links a {
  color: var(--light-gray);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  padding: 0.3125em 0;
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: width 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
  color: var(--white);
}

.nav-links a:hover::after,
.nav-links a.active::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  gap: 15px;
}

.btn-login {
  background: transparent;
  color: var(--white);
  padding: 10px 25px;
  border-radius: 30px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-signup {
  background: var(--white);
  color: var(--black);
  padding: 10px 25px;
  border-radius: 30px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-login:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-signup:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Hero Section */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
              url('https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80') no-repeat center center/cover;
  padding-top: 80px;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 0 20px;
}

.hero h1 {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #fff, var(--light-gray));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.1;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  color: var(--light-gray);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Search Bar */
.search-bar {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  padding: 10px 20px;
  max-width: 600px;
  margin: 0 auto;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-bar i {
  color: var(--light-gray);
  margin-right: 15px;
  font-size: 1.2rem;
}

.search-bar input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--white);
  font-size: 1rem;
  padding: 10px 0;
  outline: none;
}

.search-bar input::placeholder {
  color: var(--medium-gray);
}

/* Carousel Styles */
.carousel-container {
  position: relative;
  max-width: 1200px;
  margin: 100px auto;
  overflow: hidden;
  padding: 0 5vw;
}

.carousel-track {
  display: flex;
  align-items: flex-end;
  transition: transform 0.5s ease;
  height: 400px;
  padding: 20px 0;
}

.artist-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  text-decoration: none;
  color: white;
  transition: all 0.5s ease;
  position: relative;
  cursor: pointer;
  flex: 0 0 auto;
  margin: 0 10px;
  width: 7.5em;
}

.artist-circle.active {
  width: 200px;
  margin: 0 1.25em;
}

.artist-image {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  transition: all 0.5s ease;
  border: 0.25em solid #1db954;
  box-shadow: 0 8px 25px rgba(29, 185, 84, 0.2);
  position: relative;
  z-index: 2;
}

.artist-circle.active .artist-image {
  width: 200%;
  padding-bottom: 200%;
  margin-bottom: 20px;
  border-width: 0.375rem;
  box-shadow: 0 15px 40px rgba(29, 185, 84, 0.4);
}

.artist-circle:nth-child(3n+1) .artist-image {
  width: 100px;
  height: 100px;
  padding-bottom: 0;
}

.artist-circle:nth-child(3n+2) .artist-image {
  width: 130px;
  height: 130px;
  padding-bottom: 0;
}

.artist-circle:nth-child(3n+3) .artist-image {
  width: 90px;
  height: 90px;
  padding-bottom: 0;
}

.artist-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: white;
  text-align: center;
  margin-top: 15px;
  opacity: 0.7;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.artist-circle.active .artist-name {
  font-size: 1.2rem;
  opacity: 1;
  font-weight: 600;
  margin-top: 25px;
}

/* Carousel Navigation Buttons */
.carousel-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  backdrop-filter: blur(5px);
}

.carousel-button:hover {
  background: #1db954;
  transform: translateY(-50%) scale(1.1);
}

.carousel-button.prev {
  left: 10px;
}

.carousel-button.next {
  right: 10px;
}

/* Center active item */
.carousel-track {
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
}

.carousel-track::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome, Safari and Opera */
}

.artist-circle {
  scroll-snap-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .carousel-container {
    padding: 0 50px;
  }
  
  .artist-image {
    width: 130px;
    height: 130px;
  }
  
  .artist-name {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .carousel-container {
    padding: 0 40px;
  }
  
  .artist-image {
    width: 110px;
    height: 110px;
  }
  
  .carousel-button {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    padding: 0 30px;
  }
  
  .artist-image {
    width: 90px;
    height: 90px;
  }
  
  .artist-name {
    font-size: 0.875rem;
  }
  
  .carousel-button {
    width: 1.5625em;
    height: 1.5625em;
    font-size: 0.75em;
  }
}
.featured-artists {
  padding: 6.25em 0;
  background-color: var(--black);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5em;
}

.view-all {
  color: var(--primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

.view-all:hover {
  color: var(--white);
}

.artist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 30px;
  margin-top: 2.5em;
}

.artist-card {
  background: var(--dark-gray);
  border-radius: 10px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: left;
  color: var(--white);
  text-decoration: none;
  position: relative;
}

.artist-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.artist-image {
  width: 100%;
  padding-top: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  transition: all 0.5s ease;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.artist-card:hover .overlay {
  opacity: 1;
}

.overlay span {
  background: var(--primary);
  color: var(--white);
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.artist-card:hover .overlay span {
  transform: translateY(0);
}

.artist-card h3 {
  font-size: 1.2rem;
  margin: 0.9375em 0.9375em 0.3125em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.artist-card p {
  color: var(--light-gray);
  font-size: 0.9rem;
  margin: 0 0.9375em 1.25em;
}

/* Trending Now */
.trending-now {
  padding: 0 0 6.25em;
  background-color: var(--black);
}

.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.trending-card {
  border-radius: 15px;
  overflow: hidden;
  height: 40vh;
  position: relative;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-end;
  padding: 30px;
  transition: transform 0.3s ease;
}

.trending-card:hover {
  transform: translateY(-10px);
}

.badge {
  position: absolute;
  top: 1.25em;
  right: 1.25em;
  background: #ff4757;
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.trending-content {
  position: relative;
  z-index: 2;
  text-align: left;
  width: 100%;
}

.trending-content h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

.trending-content p {
  color: var(--light-gray);
  margin-bottom: 20px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

.btn-play {
  background: var(--primary);
  color: white;
  border: none;
  padding: 10px 25px;
  border-radius: 30px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-play:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

/* Artist Page */
.artist-page {
  padding-top: 80px;
  min-height: 100vh;
}

.artist-header {
  padding: 6.25em 0 3.125em;
  background-size: cover;
  background-position: center;
  position: relative;
}

.artist-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
  z-index: 1;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: var(--light-gray);
  margin-bottom: 30px;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  z-index: 2;
}

.back-btn:hover {
  color: var(--white);
}

.artist-info {
  position: relative;
  z-index: 2;
  text-align: left;
  max-width: 800px;
}

.artist-info h1 {
  font-size: clamp(2.5rem, 10vw, 4.5rem);
  margin-bottom: 20px;
  line-height: 1.1;
}

.artist-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  color: var(--light-gray);
}

.artist-stats span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.artist-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.artist-tabs {
  display: flex;
  gap: 20px;
  margin: 40px 0 30px;
  border-bottom: 1px solid var(--medium-gray);
  padding-bottom: 10px;
}

.tab {
  background: none;
  border: none;
  color: var(--light-gray);
  font-size: 1rem;
  font-weight: 600;
  padding: 10px 0;
  position: relative;
  cursor: pointer;
  transition: color 0.3s ease;
}

.tab::after {
  content: '';
  position: absolute;
  bottom: -11px;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--primary);
  transition: width 0.3s ease;
}

.tab.active {
  color: var(--white);
}

.tab.active::after {
  width: 100%;
}

.artist-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 60px;
}

.detail-card {
  background: var(--dark-gray);
  border-radius: 10px;
  padding: 1.5625em;
}

.detail-card h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.event {
  display: flex;
  align-items: center;
  padding: 0.9375em 0;
  border-bottom: 1px solid var(--medium-gray);
}

.event:last-child {
  border-bottom: none;
}

.event-date {
  text-align: center;
  margin-right: 1.25em;
  min-width: 3.75em;
}

.event-date .day {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.event-date .month {
  font-size: 0.8rem;
  text-transform: uppercase;
  color: var(--light-gray);
  letter-spacing: 1px;
}

.event-info h4 {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.event-info p {
  font-size: 0.9rem;
  margin: 0;
  color: var(--light-gray);
}

.btn-ticket {
  margin-left: auto;
  background: var(--primary);
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-ticket:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.track {
  display: flex;
  align-items: center;
  padding: 0.75em 0;
  border-bottom: 1px solid var(--medium-gray);
}

.track-number {
  width: 1.875em;
  color: var(--light-gray);
  font-size: 0.9rem;
}

.track-img {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  margin-right: 15px;
  object-fit: cover;
}

.track-info h4 {
  font-size: 1rem;
  margin-bottom: 5px;
}

.track-info p {
  font-size: 0.85rem;
  margin: 0;
  color: var(--light-gray);
}

.track-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 15px;
}

.duration {
  color: var(--light-gray);
  font-size: 0.9rem;
}

.artist-about {
  background: var(--dark-gray);
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 60px;
}

.genre-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 0.9375em 0 30px;
}

.genre-tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.similar-artists {
  display: flex;
  gap: 20px;
  margin-top: 1.25em;
}

.similar-artist {
  text-align: center;
  width: 100px;
}

.similar-artist img {
  width: 5em;
  height: 5em;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 10px;
  border: 2px solid var(--primary);
  padding: 3px;
}

.similar-artist span {
  font-size: 0.9rem;
  color: var(--light-gray);
}

/* Footer */
.footer {
  background: #000;
  padding: 3.75em 0 0;
  color: var(--light-gray);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
}

.footer-section h4 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-section h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background: var(--primary);
}

.footer-section p {
  margin-bottom: 20px;
  line-height: 1.7;
}

.footer-section a {
  display: block;
  color: var(--light-gray);
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--primary);
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  color: var(--light-gray);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--primary);
  color: var(--white);
  transform: translateY(-3px);
}

.footer-bottom {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 0.9rem;
  color: var(--medium-gray);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  h1 {
    font-size: 3rem;
  }
  
  .artist-info h1 {
    font-size: 3.5rem;
  }
  
  .artist-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 15px 5%;
  }
  
  .nav-links {
    display: none;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .artist-info h1 {
    font-size: 2.5rem;
  }
  
  .artist-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .artist-actions {
    flex-wrap: wrap;
  }
  
  .trending-grid {
    grid-template-columns: 1fr;
  }
  
  .similar-artists {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .nav-actions {
    display: none;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
  
  .search-bar {
    padding: 8px 15px;
  }
  
  .artist-info h1 {
    font-size: 2rem;
  }
  
  .artist-tabs {
    overflow-x: auto;
    padding-bottom: 5px;
    -webkit-overflow-scrolling: touch;
  }
  
  .artist-tabs::-webkit-scrollbar {
    display: none;
  }
  
  .tab {
    white-space: nowrap;
  }
}

.progress-bar span {
  display: block;
  width: 60%;
  height: 100%;
  background: #ff80ab;
}