/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a2e 50%, #16213e 100%);
    color: #e0e0e0;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
  }
  
  /* Animated background particles */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 127, 0.1) 0%, transparent 50%);
    animation: shimmer 20s ease-in-out infinite alternate;
    z-index: -1;
  }
  
  @keyframes shimmer {
    0% {
      transform: translateX(-10px) translateY(-10px);
      opacity: 0.5;
    }
    100% {
      transform: translateX(10px) translateY(10px);
      opacity: 0.8;
    }
  }
  
  /* Header styles */
  h1 {
    text-align: center;
    padding: 3rem 2rem;
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, #9c27b0, #00ff7f, #8e24aa);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 8s ease infinite;
    text-shadow: 0 0 50px rgba(156, 39, 176, 0.5);
    letter-spacing: 0.2em;
    position: relative;
  }
  
  h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #9c27b0, #00ff7f, transparent);
    border-radius: 2px;
    animation: pulse 2s ease-in-out infinite;
  }
  
  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 0.6; transform: translateX(-50%) scaleX(1); }
    50% { opacity: 1; transform: translateX(-50%) scaleX(1.2); }
  }
  
  /* Container styles */
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  /* Artists grid */
  .artists-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
  }
  
  /* Artist card styles */
  .artist-card {
    background: rgba(30, 30, 46, 0.8);
    border: 2px solid rgba(156, 39, 176, 0.2);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    cursor: pointer;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  .artist-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      rgba(156, 39, 176, 0.1) 0%, 
      transparent 50%, 
      rgba(0, 255, 127, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }
/*   
  .artist-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: #9c27b0;
    box-shadow: 0 20px 60px rgba(156, 39, 176, 0.4),
                0 0 40px rgba(0, 255, 127, 0.2);
  }
  
  .artist-card:hover::before {
    opacity: 1;
  }
   */
  /* Artist image styles */
  .artist-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
  }
  
  .artist-card:hover .artist-image {
    transform: scale(1.1);
    filter: brightness(1.2) saturate(1.3);
  }
  
  /* Artist name styles */
  .artist-name {
    padding: 1.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(45deg, #e0e0e0, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
  }
  
  .artist-card:hover .artist-name {
    background: linear-gradient(45deg, #9c27b0, #00ff7f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: translateY(-5px);
  }
  
  /* View more button styles */
  .view-more {
    display: block;
    text-align: center;
    padding: 1rem 2rem;
    margin: 0 1.5rem 1.5rem;
    background: linear-gradient(45deg, #9c27b0, #8e24aa);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 3;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }
  
  .view-more::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }
  
  .view-more:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(156, 39, 176, 0.5);
    background: linear-gradient(45deg, #00ff7f, #32cd32);
  }
  
  .view-more:hover::before {
    left: 100%;
  }
  
  .view-more:active {
    transform: translateY(0);
  }
  
  /* Responsive design */
  @media (max-width: 768px) {
    h1 {
      font-size: 2.5rem;
      padding: 2rem 1rem;
    }
    
    .container {
      padding: 1rem;
    }
    
    .artists-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
    }
    
    .artist-card {
      border-radius: 15px;
    }
    
    .artist-image {
      height: 200px;
    }
    
    .artist-name {
      font-size: 1.3rem;
      padding: 1rem;
    }
    
    .view-more {
      margin: 0 1rem 1rem;
      padding: 0.8rem 1.5rem;
      font-size: 0.9rem;
    }
  }
  
  @media (max-width: 480px) {
    h1 {
      font-size: 2rem;
      letter-spacing: 0.1em;
    }
    
    .artists-container {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .artist-card {
      margin: 0 0.5rem;
    }
  }
  
  /* Loading animation for images */
  .artist-image {
    animation: fadeIn 0.8s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Selection colors */
  ::selection {
    background: rgba(156, 39, 176, 0.3);
    color: white;
  }
  
  ::-moz-selection {
    background: rgba(156, 39, 176, 0.3);
    color: white;
  }