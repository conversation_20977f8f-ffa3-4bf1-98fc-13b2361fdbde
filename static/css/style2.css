/* Base Styles - Fixed Alignment Issues */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary: #1db954;
  --primary-dark: #1aa34a;
  --black: #0f0f0f;
  --dark-gray: #181818;
  --medium-gray: #282828;
  --light-gray: #b3b3b3;
  --white: #ffffff;
  --gradient: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  --shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #0f0f0f;
  color: white;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
  color: inherit;
}

/* Container - Fixed centering */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

/* Typography - Fixed spacing */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 1rem 0;
  text-align: center;
}

h1 {
  font-size: clamp(2rem, 5vw, 4rem);
}

h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  position: relative;
  display: inline-block;
}

h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 4px;
  background: var(--primary);
  border-radius: 2px;
}

p {
  color: var(--light-gray);
  margin-bottom: 1.5rem;
  text-align: left;
}

/* Header Section - Fixed centering */
.header-section {
  text-align: center;
  padding: 3rem 0;
  margin-bottom: 2rem;
}

.header-mid {
  font-size: clamp(2.5rem, 6vw, 4rem);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  background: linear-gradient(45deg, #9c27b0, #00ff7f, #8e24aa);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Artist Grid - Fixed alignment */
.artists-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
  justify-items: center;
  align-items: start;
  max-width: 1400px;
  margin: 0 auto;
}

.artist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 30px;
  margin-top: 2.5rem;
  justify-items: center;
}

/* Artist Card - REMOVED HOVER FLOAT EFFECT */
.artist-card {
  background: rgba(30, 30, 46, 0.8);
  border: 2px solid rgba(156, 39, 176, 0.2);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 320px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.artist-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(156, 39, 176, 0.1) 0%, 
    transparent 50%, 
    rgba(0, 255, 127, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

/* REMOVED TRANSFORM ON HOVER - Only subtle effects remain */
.artist-card:hover {
  border-color: #9c27b0;
  box-shadow: 0 12px 40px rgba(156, 39, 176, 0.3);
}

.artist-card:hover::before {
  opacity: 1;
}

/* Artist Image - Removed scale effect */
.artist-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.artist-card:hover .artist-image {
  filter: brightness(1.1) saturate(1.1);
}

/* Artist Name - Removed transform effect */
.artist-name {
  padding: 1.5rem;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(45deg, #e0e0e0, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 3;
  transition: all 0.3s ease;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.artist-card:hover .artist-name {
  background: linear-gradient(45deg, #9c27b0, #00ff7f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* View More Button - Fixed positioning */
.view-more {
  display: block;
  text-align: center;
  padding: 1rem 2rem;
  margin: 0 1.5rem 1.5rem;
  background: linear-gradient(45deg, #9c27b0, #8e24aa);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 3;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  flex-shrink: 0;
}

.view-more::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.view-more:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(156, 39, 176, 0.5);
  background: linear-gradient(45deg, #00ff7f, #32cd32);
}

.view-more:hover::before {
  left: 100%;
}

/* Artist Page Specific Styles */
.artist-page {
  padding-top: 0;
  min-height: 100vh;
}

.artist-header {
  height: 60vh;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: flex-end;
  position: relative;
  overflow: hidden;
}

.artist-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(156, 39, 176, 0.3) 0%, 
    rgba(0, 0, 0, 0.7) 50%, 
    rgba(0, 255, 127, 0.2) 100%);
  z-index: 1;
}

/* FIXED: Artist header container - ALWAYS CENTERED */
.artist-header .container {
  position: relative;
  z-index: 2;
  padding-bottom: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* FIXED: Back Button - CENTERED for ALL devices */
.back-btn {
  display: inline-flex;
  align-items: left;
  justify-content: left;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: rgba(30, 30, 46, 0.9);
  border: 2px solid rgba(156, 39, 176, 0.5);
  border-radius: 50px;
  color: #e0e0e0;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  margin: 0 auto 2rem auto; /* CENTER with auto margins */
  position: relative;
  overflow: hidden;
  text-align: center;
  
}

.back-btn:hover {
  background: rgba(156, 39, 176, 0.8);
  border-color: #9c27b0;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.4);
}

/* FIXED: Artist Info - ALWAYS CENTERED */
.artist-info {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* FIXED: Artist title - ALWAYS CENTERED */
.artist-info h1 {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 900;
  background: linear-gradient(45deg, #ffffff, #9c27b0, #00ff7f);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 8s ease infinite;
  text-shadow: 0 0 50px rgba(156, 39, 176, 0.5);
  margin: 0;
  padding: 0;
  letter-spacing: 0.05em;
  text-align: center;
  width: 100%;
}

/* Tab Navigation - Fixed alignment */
.artist-tabs {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  border-bottom: 2px solid rgba(156, 39, 176, 0.2);
  flex-wrap: wrap;
  justify-content: flex-start;
}

.tab {
  padding: 1rem 2rem;
  background: rgba(30, 30, 46, 0.6);
  border: 2px solid rgba(156, 39, 176, 0.2);
  border-bottom: none;
  border-radius: 15px 15px 0 0;
  color: #e0e0e0;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.tab:hover {
  background: rgba(156, 39, 176, 0.3);
  border-color: #9c27b0;
  transform: translateY(-3px);
}

.tab.active {
  background: linear-gradient(45deg, #9c27b0, #8e24aa);
  border-color: #9c27b0;
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.4);
}

/* Detail Card - Fixed content alignment */
.detail-card {
  background: rgba(30, 30, 46, 0.8);
  border: 2px solid rgba(156, 39, 176, 0.2);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.detail-card:hover {
  border-color: rgba(156, 39, 176, 0.5);
  box-shadow: 0 12px 40px rgba(156, 39, 176, 0.2);
}

.detail-card h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-align: left;
}

.detail-card h3 i {
  color: #9c27b0;
  font-size: 1.5rem;
}

/* Artist Bio - Fixed layout */
.artist-bio {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
  align-items: start;
}

.artist-bio-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 15px;
  border: 2px solid rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.artist-bio-image:hover {
  transform: scale(1.05);
  border-color: #9c27b0;
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.4);
}

.artist-bio-content p {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

/* Members List - Fixed alignment */
.members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: flex-start;
}

.member-tag {
  background: linear-gradient(45deg, #9c27b0, #8e24aa);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.member-tag:hover {
  background: linear-gradient(45deg, #00ff7f, #32cd32);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 127, 0.3);
}

/* Stats Grid - Fixed layout */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  background: rgba(156, 39, 176, 0.1);
  padding: 1.5rem;
  border-radius: 15px;
  border: 2px solid rgba(156, 39, 176, 0.2);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(156, 39, 176, 0.2);
  border-color: #9c27b0;
  transform: translateY(-5px);
}

.stat-item h4 {
  font-size: 1.1rem;
  color: #9c27b0;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.stat-item p {
  font-size: 1.3rem;
  color: #ffffff;
  font-weight: 600;
  text-align: center;
}

/* Location Section - Fixed alignment */
.location-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(0, 255, 127, 0.05);
  border-radius: 15px;
  border: 2px solid rgba(0, 255, 127, 0.2);
  transition: all 0.3s ease;
}

.location-section:hover {
  background: rgba(0, 255, 127, 0.1);
  border-color: #00ff7f;
  transform: translateY(-3px);
}

.location-section h4 {
  font-size: 1.4rem;
  color: #00ff7f;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-align: left;
}

/* Dates List - Fixed grid */
.dates-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.date-item {
  background: rgba(30, 30, 46, 0.8);
  padding: 1rem;
  border-radius: 10px;
  border: 2px solid rgba(0, 255, 127, 0.2);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-weight: 600;
}

.date-item:hover {
  background: rgba(0, 255, 127, 0.1);
  border-color: #00ff7f;
  transform: translateX(10px);
}

.date-item i {
  color: #00ff7f;
  font-size: 1rem;
}

/* Responsive Design - CENTERED elements for all screen sizes */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .artists-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1rem;
  }

  .artist-card {
    max-width: 100%;
    margin: 0 auto;
  }

  .artist-header {
    height: 50vh;
  }

  /* ENSURE centering on mobile */
  .artist-header .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .back-btn {
    margin: 0 auto 2rem auto;
  }

  .artist-info {
    text-align: center;
    width: 100%;
  }

  .artist-info h1 {
    font-size: 2.5rem;
    text-align: center;
  }

  .artist-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .tab {
    text-align: center;
    border-radius: 10px;
    width: 100%;
  }

  .artist-bio {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }

  .artist-bio-image {
    width: 150px;
    height: 150px;
    justify-self: center;
  }

  .members-list {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dates-list {
    grid-template-columns: 1fr;
  }

  h2::after {
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 480px) {
  /* ENSURE centering on smallest screens */
  .artist-header .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: flex-end;
  }

  .back-btn {
    margin: 0 auto 1.5rem auto;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .artist-info {
    text-align: center;
    width: 100%;
  }

  .artist-info h1 {
    font-size: 2rem;
    text-align: center;
  }

  .artist-header {
    height: 40vh;
  }

  .detail-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .detail-card h3 {
    font-size: 1.5rem;
  }

  .artist-image {
    height: 200px;
  }

  .artist-name {
    font-size: 1.2rem;
    padding: 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.artist-card,
.artist-bio-image {
  animation: fadeIn 0.8s ease-in-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection colors */
::selection {
  background: rgba(156, 39, 176, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(156, 39, 176, 0.3);
  color: white;
}