/* Base Styles */
:root {
  --primary: #1db954;
  --dark-bg: #121212;
  --sidebar-bg: #000000;
  --right-sidebar-bg: #181818;
  --player-bg: #181818;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --hover-bg: #282828;
  --active-bg: #1a1a1a;
  --divider: #282828;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #0f0f0f;
  color: white;
  line-height: 1.6;
  padding: 1.25rem 5vw;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font: inherit;
}

/* Background */
.background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://source.unsplash.com/random/1920x1080/?music,concert")
    no-repeat center center;
  background-size: cover;
  z-index: -2;
}

.blur-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  z-index: -1;
}

/* Layout */
/* .container {
  display: flex;
  height: 100vh;
  padding-bottom: 90px; 
} */

/* Sidebar */
.sidebar {
  width: 240px;
  background-color: var(--sidebar-bg);
  display: flex;
  flex-direction: column;
  padding: 24px 12px;
  border-right: 1px solid var(--divider);
  overflow-y: auto;
}

.logo {
  display: flex;
  align-items: center;
  padding: 0 12px 18px;
  margin-bottom: 18px;
  border-bottom: 1px solid var(--divider);
}

.logo i {
  font-size: 24px;
  margin-right: 12px;
  color: var(--primary);
}

.logo span {
  font-size: 18px;
  font-weight: 700;
}

.nav-menu {
  margin-bottom: 24px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 4px 0;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  font-size: 14px;
}

.nav-item i {
  width: 24px;
  margin-right: 12px;
  text-align: center;
}

.nav-item:hover,
.nav-item.active {
  color: var(--text-primary);
  background-color: var(--hover-bg);
}

.nav-item.active {
  background-color: var(--active-bg);
  font-weight: 600;
}

.playlist-section {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid var(--divider);
}

.playlist-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.playlist-header i {
  margin-right: 12px;
}

/* Right Sidebar */
.right-sidebar {
  width: 280px;
  background-color: var(--right-sidebar-bg);
  border-left: 1px solid var(--divider);
  padding: 24px;
  overflow-y: auto;
}

.now-playing {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--divider);
}

.now-playing-cover {
  width: 56px;
  height: 56px;
  margin-right: 12px;
  background-color: #333;
  border-radius: 4px;
  overflow: hidden;
}

.now-playing-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.now-playing-info {
  flex: 1;
}

.now-playing-info h4 {
  font-size: 14px;
  margin-bottom: 4px;
}

.now-playing-info p {
  font-size: 12px;
  color: var(--text-secondary);
}

.like-btn {
  color: var(--text-secondary);
  font-size: 16px;
  padding: 8px;
  transition: color 0.2s ease;
}

.like-btn:hover {
  color: var(--primary);
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.queue-header h4 {
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--text-secondary);
}

.clear-btn {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 4px 8px;
  border-radius: 2px;
}

.clear-btn:hover {
  background-color: var(--hover-bg);
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.nav-buttons {
  display: flex;
  gap: 16px;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 16px;
}

.upgrade-btn {
  background-color: var(--text-primary);
  color: #000;
  font-weight: 600;
  font-size: 12px;
  padding: 6px 16px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: transform 0.2s ease;
}

.upgrade-btn:hover {
  transform: scale(1.05);
}

.user-avatar {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px 4px 4px;
  border-radius: 20px;
  gap: 8px;
}

.user-avatar img {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

.user-avatar span {
  font-size: 14px;
  font-weight: 600;
}

/* Center Content */
.center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px;
}

.music-icon {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  border: 2px solid var(--primary);
}

.music-icon i {
  font-size: 48px;
  color: var(--primary);
}

.center-content h1 {
  font-size: 48px;
  margin-bottom: 16px;
  background: linear-gradient(to right, #fff, #1db954);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.center-content p {
  font-size: 18px;
  color: var(--text-secondary);
  max-width: 600px;
  line-height: 1.6;
}

/* Player Controls */
.player-controls {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 90px;
  background-color: var(--player-bg);
  border-top: 1px solid var(--divider);
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.track-info {
  display: flex;
  align-items: center;
  width: 30%;
  min-width: 180px;
}

.track-info img {
  width: 56px;
  height: 56px;
  margin-right: 12px;
  border-radius: 4px;
}

.track-details h4 {
  font-size: 14px;
  margin-bottom: 4px;
}

.track-details p {
  font-size: 12px;
  color: var(--text-secondary);
}

.playback-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 40%;
  max-width: 720px;
}

.top-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.top-controls button {
  color: var(--text-secondary);
  font-size: 16px;
  transition: color 0.2s ease;
}

.top-controls button:hover {
  color: var(--text-primary);
}

.play-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--text-primary);
  color: #000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.progress-bar span {
  font-size: 11px;
  color: var(--text-secondary);
  min-width: 40px;
}

.progress {
  flex: 1;
  height: 4px;
  background-color: #4d4d4d;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.progress-filled {
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 100%;
  background-color: #1db954;
  border-radius: 2px;
}

.volume-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 30%;
  padding-right: 16px;
  gap: 8px;
}

.volume-bar {
  width: 100px;
  height: 4px;
  background-color: #4d4d4d;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.volume-level {
  position: absolute;
  top: 0;
  left: 0;
  width: 70%;
  height: 100%;
  background-color: #1db954;
  border-radius: 2px;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #4d4d4d;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Responsive */
@media (max-width: 1200px) {
  .right-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -240px;
    transition: transform 0.3s ease;
    z-index: 1000;
    height: calc(100% - 90px);
  }

  .sidebar.active {
    transform: translateX(240px);
  }

  .main-content {
    margin-left: 0;
  }

  .menu-toggle {
    display: block;
  }
}

.logo {
  width: 30%;
  height: 60%;
  /* margin-top: 20rem;
      margin-bottom: 20rem; */
}

    img.logo {
      display: block;
      border: none;
      outline: none;
      text-decoration: none;
    }

/* Base font size for rem units */
:root {
  font-size: 1rem; /* 16px */
}

/* Container */
.container {
   display: flex;
  justify-content: center;
  align-items: center;
  /* margin-top: 11rem; */
 /* min-height: 100vh; */
  width: 100%;
  /* padding: 1.25rem; 20px */
  box-sizing: border-box;
}

/* Slider */
.slider {
  position: relative;
  width: min(90vw, 30rem); /* Responsive width with max-width */
  aspect-ratio: 1; /* Maintain square aspect ratio */
  margin: 0 auto;
  max-width: 100%;
}

/* Hide radio buttons */
.slider input[type="radio"] {
  display: none;
}

/* Slides container */
.slides {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 0;
}

/* Individual slide */
.slide {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Show first slide by default */
#slide1:checked ~ .slides #slide-1 {
  opacity: 1;
  z-index: 1;
}

/* Show second slide when second radio is checked */
#slide2:checked ~ .slides #slide-2 {
  opacity: 1;
  z-index: 1;
}

/* Image styling */
.slide img.logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;

}

/* Navigation arrows */
.controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.controls label {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 2.5rem; /* 40px */
  height: 2.5rem; /* 40px */
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem; /* 18px */
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.3s ease;
}

.controls label:hover {
  background: rgba(0, 0, 0, 0.8);
}

.controls .prev {
  left: -3.125rem; /* -50px */
}

.controls .next {
  right: -3.125rem; /* -50px */
}

/* Responsive Design */
@media (max-width: 48rem) { /* 768px */
  :root {
    font-size: 0.875rem; /* Adjust base font size for smaller screens */
  }
  
  .slider {
    width: min(90vw, 25rem);
  }
  
  .controls label {
    width: 2.25rem;
    height: 2.25rem;
    font-size: 1rem;
  }
  
  .controls .prev {
    left: -2.5rem;
  }
  
  .controls .next {
    right: -2.5rem;
  }
}

